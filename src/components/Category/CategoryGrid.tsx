'use client';

import React from 'react';
import * as Icon from 'react-feather';
import Link from 'next/link';
import Image from 'next/image';
import { TCategoryData } from '@/types/blog/category';
import { assets, generatePlaceholderImage, getAltImage } from '@/utils/helper';
import { useTranslations } from 'next-intl';

interface CategoryGridProps {
  categories: TCategoryData[];
}

const CategoryGrid: React.FC<CategoryGridProps> = ({ categories }) => {
  const t = useTranslations('pages.categories');

  return (
    <>
      {categories.map((category) => (
        <div key={category.id} className="col-lg-4 col-md-6">
          <div className="single-blog-post-box single-category-post">
            <div className="entry-thumbnail">
              <Link href={`/blog/category/${category.slug}`}>
                <Image
                  src={assets(category.image)}
                  alt={getAltImage(category.image)}
                  width={860}
                  height={700}
                />
              </Link>
            </div>

            <div className="entry-post-content category-post-content">
              <div className="entry-meta">
                <ul>
                  <li>
                    <Link href={`/blog/category/${category.slug}`}>
                      {category.name}
                    </Link>
                  </li>
                  <li>{category.posts_count || 0} {t(category.posts_count > 1 ? 'posts' : 'post')}</li>
                </ul>
              </div>

              <h3>
                <Link href={`/blog/category/${category.slug}`} className="line-clamp-2">
                  {category.name}
                </Link>
              </h3>

              {category.description && (
                <p dangerouslySetInnerHTML={{ __html: category.description }} className="line-clamp-4" />
              )}

              <Link
                href={`/blog/category/${category.slug}`}
                className="learn-more-btn category-post-learn-more"
              >
                Read Story <Icon.Plus />
              </Link>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

export default CategoryGrid;
