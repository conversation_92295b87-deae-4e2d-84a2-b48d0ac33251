'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import * as Icon from 'react-feather';
import Pagination from '@/components/Common/Pagination';
import { TBlogPost } from '@/types/blog';
import { assets, formatDate, generatePlaceholderImage, getAltImage } from '@/utils/helper';
import { useTranslations } from 'next-intl';

interface CategoryBlogGridProps {
  blogs: TBlogPost[];
  meta?: any;
  categoryName: string;
}

const CategoryBlogGrid: React.FC<CategoryBlogGridProps> = ({ blogs, meta, categoryName }) => {
  const t = useTranslations('pages.categories');

  return (
    <>
      <div className="blog-area ptb-80">
        <div className="container">
          <div className="row">
            {blogs && blogs.length > 0 ? (
              blogs.map((blog) => (
                <div key={blog.id} className="col-lg-4 col-md-6">
                  <div className="single-blog-post-item">
                    <div className="post-image">
                      <Link href={`/blog/${blog.slug}`}>
                        <Image
                          src={assets(blog.image)}
                          alt={blog.name}
                          width={860}
                          height={700}
                        />
                      </Link>
                    </div>

                    <div className="post-content">
                      <ul className="post-meta">
                        <li>
                          <Link href={`/author/${blog.author?.full_name || '#'}`}>
                            {blog.author?.full_name || 'Admin'}
                          </Link>
                        </li>
                        <li>{formatDate(blog.created_at)}</li>
                      </ul>
                      <h3>
                        <Link href={`/blog/${blog.slug}`}>
                          {blog.name}
                        </Link>
                      </h3>

                      <Link
                        href={`/blog/${blog.slug}`}
                        className="read-more-btn"
                      >
                        Read More <Icon.PlusCircle />
                      </Link>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-lg-12">
                <div className="text-center">
                  <h3>{t('no-posts')}</h3>
                  <p>{t('check-back')}</p>
                </div>
              </div>
            )}

            {/* Pagination */}
            {meta && meta.last_page > 1 && (
              <div className="col-lg-12 col-md-12">
                <Pagination
                  currentPage={meta.current_page}
                  lastPage={meta.last_page}
                  total={meta.total}
                  perPage={meta.per_page}
                  from={meta.from}
                  to={meta.to}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default CategoryBlogGrid;
