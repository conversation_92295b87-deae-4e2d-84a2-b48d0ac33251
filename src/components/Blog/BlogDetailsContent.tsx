'use client';

import React, { useEffect, useRef } from 'react';
import Link from 'next/link';
import * as Icon from 'react-feather';
import Image from 'next/image';
import BlogSidebar from '../../components/Blog/BlogSidebar';
import CommentList from './CommentList';
import { TBlogPost } from '@/types/blog';
import { assets, formatDate } from '@/utils/helper';

import blogImg1 from '/public/images/blog-image/blog7.jpg';
import blogImg2 from '/public/images/blog-image/blog8.jpg';
import { useTranslations, useLocale } from 'next-intl';

interface BlogDetailsContentProps {
  blogPost: TBlogPost;
}

const BlogDetailsContent: React.FC<BlogDetailsContentProps> = ({ blogPost }) => {
  const shadowRef = useRef<HTMLDivElement>(null);
  const tCommon = useTranslations('common');
  const locale = useLocale();

  useEffect(() => {
    if (!shadowRef.current) {
      return;
    }
    if (shadowRef.current.shadowRoot) {
      return;
    }

    const shadow = shadowRef.current.attachShadow({ mode: 'open' });

    const links = [
      `${process.env.NEXT_PUBLIC_APP_URL}/themes/ripple/css/style.css?v=7.5.5`,
      `${process.env.NEXT_PUBLIC_APP_URL}/vendor/core/core/base/libraries/ckeditor/content-styles.css`,
      `${process.env.NEXT_PUBLIC_APP_URL}/themes/ripple/css/style.integration.css`
    ];

    links.forEach((href) => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = href;
      shadow.appendChild(link);
    });

    const wrapperContainer = document.createElement('div');
    wrapperContainer.className = 'page-content';

    const wrapperArticle = document.createElement('article');
    wrapperArticle.className = 'post post--single';
    wrapperContainer.appendChild(wrapperArticle);

    const wrapperPostContent = document.createElement('div');
    wrapperPostContent.className = 'post__content';
    wrapperArticle.appendChild(wrapperPostContent);

    const wrapper = document.createElement('div');
    wrapper.className = 'ck-content';
    wrapper.innerHTML = blogPost.content ?? '';
    wrapperPostContent.appendChild(wrapper);

    shadow.appendChild(wrapperContainer);

    const imgs = shadow.querySelectorAll('img');
    imgs.forEach((img) => {
      img.style.height = 'auto';
    });
  }, [blogPost]);

  return (
    <>
      <div className="blog-details-area ptb-80">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 col-md-12">
              <div className="blog-details-desc">
                {blogPost.image && (
                  <div className="article-image">
                    <Image
                      src={assets(blogPost.image)}
                      alt={blogPost.name}
                      width={860}
                      height={700}
                    />
                  </div>
                )}

                <div className="article-content">
                  <div className="entry-meta">
                    <ul>
                      <li>
                        <Icon.Clock /> {formatDate(blogPost.updated_at)}
                      </li>
                      <li>

                        {blogPost.author.image ?
                          <Image
                            src={blogPost.author.image}
                            alt="image"
                            width={20}
                            height={20}
                            className="rounded-circle"
                          /> :
                          <Icon.User />}
                        <span>{' '}{blogPost.author?.full_name || 'CSlant'}</span>

                      </li>
                    </ul>
                  </div>

                  <h2>{blogPost.name}</h2>

                  {blogPost.description && (
                    <p className="blog-description">
                      {blogPost.description}
                    </p>
                  )}

                  {/*POST CONTENT*/}
                  <div
                    ref={shadowRef}
                    className="blog-content"
                    dangerouslySetInnerHTML={{ __html: blogPost.content }}
                  />
                  {/*END POST CONTENT*/}
                </div>

                <div className="article-footer">
                  <p className="title-categories pb-0 mb-1">
                    {tCommon(blogPost.categories.length > 1 ?
                      'categories' :
                      'category')}
                  </p>
                  <div className="article-tags">
                    {blogPost.categories?.map((category) => (
                      <Link key={category.id} href={`/${locale}/blog/category/${category.slug}`}>
                        {category.name}
                      </Link>
                    ))}
                  </div>
                </div>

                <div className="article-footer mt-2">
                  <p className="title-tags pb-0 mb-1">
                    {tCommon(blogPost.tags.length > 1 ? 'tags' : 'tags')}
                  </p>
                  <div className="article-tags">
                    {blogPost.tags?.map((tag) => (
                      <Link key={tag.id} href={`/${locale}/blog/tag/${tag.slug}`}>
                        {tag.name}
                      </Link>
                    ))}
                  </div>
                </div>

                <div className="startp-post-navigation">
                  <div className="prev-link-wrapper">
                    <div className="info-prev-link-wrapper">
                      <Link href="#">
                        <span className="image-prev">
                          <Image
                            src={blogImg1}
                            alt="image"
                            width={860}
                            height={700}
                          />
                          <span className="post-nav-title">Prev</span>
                        </span>

                        <span className="prev-link-info-wrapper">
                          <span className="prev-title">
                            Don&apos;t buy a tech gift until you read these rules
                          </span>
                          <span className="meta-wrapper">
                            <span className="date-post">January 21, 2024</span>
                          </span>
                        </span>
                      </Link>
                    </div>
                  </div>

                  <div className="next-link-wrapper">
                    <div className="info-next-link-wrapper">
                      <Link href="#">
                        <span className="next-link-info-wrapper">
                          <span className="next-title">
                            The golden rule of buying a phone as a gift
                          </span>
                          <span className="meta-wrapper">
                            <span className="date-post">January 21, 2024</span>
                          </span>
                        </span>

                        <span className="image-next">
                          <Image
                            src={blogImg2}
                            alt="image"
                            width={860}
                            height={700}
                          />
                          <span className="post-nav-title">Next</span>
                        </span>
                      </Link>
                    </div>
                  </div>
                </div>

                {/* Dynamic Comment System */}
                <CommentList postId={blogPost.id} />
              </div>
            </div>

            <div className="col-lg-4 col-md-12">
              <BlogSidebar blogPost={blogPost} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BlogDetailsContent;
