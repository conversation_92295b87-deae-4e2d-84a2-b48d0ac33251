'use client';

import React from 'react';
import Link from 'next/link';
import * as Icon from 'react-feather';
import { TTagData } from '@/types/blog/tag';
import { useTranslations } from 'next-intl';

interface TagGridProps {
  tags: TTagData[];
}

const TagGrid: React.FC<TagGridProps> = ({ tags }) => {
  const t = useTranslations('pages.tags');
  return (
    <>
      {tags.map((tag) => (
        <div key={tag.id} className="col-lg-4 col-md-6">
          <div className="single-blog-post-item">
            <div className="post-content">
              <ul className="post-meta">
                <h3>
                  <Link href={`/blog/tag/${tag.slug}`}>
                    {tag.name}
                  </Link>
                </h3>
                <li>{tag.posts_count || 0} {t(tag.posts_count > 1 ? 'posts' : 'post')}</li>
              </ul>


              <Link
                href={`/blog/tag/${tag.slug}`}
                className="read-more-btn"
              >
                Read More <Icon.PlusCircle />
              </Link>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

export default TagGrid;
