'use client';

import React from 'react';
import Link from 'next/link';
import { TTagData } from '@/types/blog/tag';
import { useTranslations } from 'next-intl';

interface TagGridProps {
  tags: TTagData[];
}

const TagGrid: React.FC<TagGridProps> = ({ tags }) => {
  const t = useTranslations('pages.tags');
  return (
    <>
      {tags.map((tag) => (
        <div key={tag.id} className="col-lg-4 col-md-6">
          <div className="single-blog-post">
            <div className="blog-post-content">
              <div className='d-flex align-items-center gap-2'>
                <h3>
                  <Link href={`/blog/tag/${tag.slug}`} className='line-clamp-2'>
                    {tag.name}
                  </Link>
                </h3>
              </div>

              <p className="line-clamp-5">
                {tag.description || 'Explore posts related to this tag.'}
              </p>

              <Link
                href={`/blog/tag/${tag.slug}`}
                className="read-more-btn"
              >
                {t('view-post', { count: tag.posts_count })}
              </Link>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

export default TagGrid;
