import React from 'react';
import Navbar from '@/components/Layout/Navbar';
import Footer from '@/components/Layout/Footer';
import PageBanner from '@/components/Common/PageBanner';
import CategoryBlogGrid from '@/components/Category/CategoryBlogGrid';
import { Metadata } from 'next';
import { getLocale, getTranslations } from 'next-intl/server';
import { generateMetadataTitle } from '@/utils/seo';
import { categoryApi } from '@/api/blog/category';
import { blogPostApi } from '@/api/blog/post';
import { notFound } from 'next/navigation';
import { PAGE_SIZE_12 } from '@/types/page';

interface CategoryPageProps {
  params: Promise<{
    slug: string;
    locale: string;
  }>;
  searchParams: Promise<{
    page?: string;
    per_page?: string;
  }>;
}

async function fetchCategoryBySlug(slug: string) {
  try {
    const response = await categoryApi.get({
      slug: slug
    });
    return response;
  } catch (error) {
    console.error('Error fetching category:', error);
    throw error;
  }
}

async function fetchBlogsByCategory(categorySlug: string, page: number, perPage: number) {
  try {
    return await blogPostApi.get({
      params: {
        category_slug: categorySlug,
        page,
        per_page: perPage
      }
    });
  } catch (error) {
    console.error('Error fetching blogs by category:', error);
    throw error;
  }
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { slug } = await params;
  const locale = await getLocale();
  const t = await getTranslations('pages.categories');

  let categoryName = slug;
  try {
    const categoryResponse = await fetchCategoryBySlug(slug);
    if (categoryResponse.data?.name) {
      categoryName = categoryResponse.data.name;
    }
  } catch (error) {
    return notFound();
  }

  const url = `https://cslant.com/${locale !== 'en' ? locale + '/' : ''}categories/${slug}`;
  const title = `${categoryName} | ${t('title', { default: 'Categories' })}`;
  const description = `Browse all blog posts in ${categoryName} category.`;
  const imageUrl = 'https://cslant.com/images/og-categories.jpg';

  const titleConfig = {
    title: title,
    template: '%s | CSlant'
  };

  return {
    ...generateMetadataTitle(titleConfig),
    description: description,
    creator: 'CSlant',
    authors: [{ name: 'CSlant', url: 'https://cslant.com' }],
    publisher: 'CSlant',
    alternates: {
      canonical: url,
      languages: {
        en: `https://cslant.com/category/${slug}`,
        vi: `https://cslant.com/vi/category/${slug}`,
      },
    },
    openGraph: {
      title: title,
      description: description,
      url: url,
      siteName: 'CSlant',
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: locale === 'vi' ? 'vi_VN' : 'en_US',
      type: 'article',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
      images: [imageUrl],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default async function CategoryPage({ params, searchParams }: CategoryPageProps) {
  const { slug } = await params;
  const searchParamsData = await searchParams;
  const page = Number(searchParamsData.page) || 1;
  const perPage = PAGE_SIZE_12;

  const t = await getTranslations('pages.categories');

  let categoryResponse;
  let blogsResponse;

  try {
    categoryResponse = await fetchCategoryBySlug(slug);
    if (!categoryResponse.data || categoryResponse.error) {
      notFound();
    }

    blogsResponse = await fetchBlogsByCategory(slug, page, perPage);
    if (!blogsResponse.data || blogsResponse.error) {
      notFound();
    }
  } catch (error) {
    notFound();
  }

  const categoryName = categoryResponse.data.name || slug;

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={categoryName} />

      <CategoryBlogGrid
        blogs={blogsResponse.data}
        meta={blogsResponse.meta}
        categoryName={categoryName}
      />

      <Footer />
    </>
  );
};
